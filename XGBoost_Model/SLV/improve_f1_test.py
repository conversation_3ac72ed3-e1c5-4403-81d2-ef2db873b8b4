#!/usr/bin/env python3
"""
改进F1-Score的测试脚本
尝试多种方法提高模型性能
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score
import matplotlib.pyplot as plt
from sklearn.metrics import precision_recall_curve, roc_curve

def load_model_and_config():
    """加载模型和配置"""
    print("📁 加载模型和配置...")
    
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功")
    print(f"   当前阈值: {threshold_config['best_threshold']}")
    
    return model, threshold_config

def create_test_windows():
    """创建测试窗口数据（使用步长1进行实时模拟）"""
    print("\n🪟 创建测试窗口数据...")
    
    # 加载原始测试数据
    test_raw = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_raw.shape}")
    
    # 转换异常标签
    test_raw['anomaly'] = test_raw['is_string_imbalance'].map({
        'True': 1, 'False': 0, True: 1, False: 0
    })
    
    # 使用步长1创建滑动窗口（模拟实时检测）
    window_size = 30
    step_size = 1  # 步长1，模拟实时检测
    test_raw = test_raw.sort_values('ts').reset_index(drop=True)
    
    # 创建滑动窗口
    windows_data = []
    window_id = 1
    
    # 确保有足够的数据创建至少一个完整窗口
    if len(test_raw) < window_size:
        print(f"   警告: 数据不足，需要至少 {window_size} 行数据")
        return pd.DataFrame()
    
    # 创建滑动窗口
    for start_idx in range(0, len(test_raw) - window_size + 1, step_size):
        end_idx = start_idx + window_size
        window_data = test_raw.iloc[start_idx:end_idx].copy()
        window_data['window_id'] = window_id
        window_data['window_start_idx'] = start_idx
        window_data['window_end_idx'] = end_idx - 1
        windows_data.append(window_data)
        window_id += 1
    
    # 合并所有窗口数据
    test_windows = pd.concat(windows_data, ignore_index=True)
    
    n_windows = test_windows['window_id'].max()
    print(f"   创建了 {n_windows} 个滑动窗口（窗口大小30，步长1）")
    print(f"   总数据点: {len(test_windows)} (包含重叠)")
    print(f"   原始数据点: {len(test_raw)}")
    
    # 统计异常分布
    anomaly_count = test_raw['anomaly'].sum()
    print(f"   原始数据异常率: {anomaly_count}/{len(test_raw)} ({anomaly_count/len(test_raw)*100:.1f}%)")
    
    return test_windows

def extract_features_like_training(test_windows):
    """严格按照训练时的方法提取特征"""
    print("\n🔧 提取特征（严格按照训练流程）...")
    
    # 定义核心特征（与训练脚本完全一致）
    CORE_FEATURES = [
            'cellvdelta',        # 电芯电压差（最重要）
            'averagecellv',      # 平均电芯电压
            'hcellv',           # 最高电芯电压
            'lcellv',           # 最低电芯电压
            'systemvolt',       # 系统电压
            'totalcurrenta',    # 总电流
            'soc',              # SOC
            'htempc',           # 最高温度
            'ltempc',           # 最低温度
            'tempcdelta',
            'averagecelltempc',
            'pcspowerset',
            'pcspower'
        ]
    
    # 检查特征可用性
    available_features = [f for f in CORE_FEATURES if f in test_windows.columns]
    print(f"   可用核心特征: {len(available_features)}/{len(CORE_FEATURES)}")
    
    # 按窗口聚合原始特征（与训练时完全一致）
    print("   聚合原始特征...")
    test_agg_features = test_windows.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    # 展平多级列名（与训练时完全一致）
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    print(f"   聚合特征形状: {test_agg_features.shape}")
    
    return test_agg_features, available_features

def create_mock_tsfresh_features(test_agg_features, available_features):
    """创建模拟的TSFresh特征（匹配训练时的特征名称）"""
    print("\n⚙️ 创建TSFresh特征...")
    
    # 从特征重要性文件中获取TSFresh特征名称
    try:
        feature_importance = pd.read_csv('results/feature_importance.csv')
        tsfresh_feature_names = [f for f in feature_importance['feature'] if '__' in f]
        print(f"   从特征重要性文件中找到 {len(tsfresh_feature_names)} 个TSFresh特征")
    except:
        print("   无法加载特征重要性文件，使用默认TSFresh特征")
        tsfresh_feature_names = []
    
    # 创建TSFresh特征DataFrame
    n_windows = len(test_agg_features)
    tsfresh_features = pd.DataFrame()
    
    if tsfresh_feature_names:
        # 使用训练时的特征名称，填充模拟值
        for feature_name in tsfresh_feature_names:
            # 从特征名称中提取基础特征名
            base_feature = feature_name.split('__')[0]
            
            if base_feature in available_features:
                # 根据聚合特征创建对应的TSFresh特征
                if '__maximum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_max']
                elif '__minimum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_min']
                elif '__mean' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean']
                elif '__standard_deviation' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_std'].fillna(0)
                elif '__median' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_median']
                elif '__variance' in feature_name:
                    tsfresh_features[feature_name] = (test_agg_features[f'{base_feature}_std'] ** 2).fillna(0)
                else:
                    # 对于其他TSFresh特征，使用均值作为默认值
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean'].fillna(0)
            else:
                # 如果基础特征不可用，填充0
                tsfresh_features[feature_name] = np.zeros(n_windows)
    
    print(f"   TSFresh特征形状: {tsfresh_features.shape}")
    
    return tsfresh_features

def create_test_labels(test_windows):
    """创建测试标签"""
    print("\n🏷️ 创建测试标签...")
    
    # 按窗口聚合标签（窗口中有任何异常则标记为异常）
    window_labels = test_windows.groupby('window_id')['anomaly'].max().reset_index()
    y_test = window_labels['anomaly'].values
    
    print(f"   窗口标签分布:")
    print(f"     正常窗口: {np.sum(y_test==0)}")
    print(f"     异常窗口: {np.sum(y_test==1)}")
    print(f"     窗口异常率: {np.mean(y_test)*100:.1f}%")
    
    return y_test

def optimize_threshold_for_f1(model, X_test, y_test):
    """优化阈值以最大化F1-Score"""
    print("\n🎯 优化阈值以最大化F1-Score...")

    # 获取模型期望的特征名称并重新排序
    expected_features = model.get_booster().feature_names
    missing_features = [f for f in expected_features if f not in X_test.columns]

    if missing_features:
        print(f"   警告: 缺失 {len(missing_features)} 个特征，用0填充")
        for feature in missing_features:
            X_test[feature] = 0

    X_test_ordered = X_test[expected_features]

    # 获取预测概率
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]

    # 尝试不同的阈值
    thresholds = np.arange(0.05, 0.95, 0.01)
    f1_scores = []
    precision_scores = []
    recall_scores = []

    for threshold in thresholds:
        y_pred = (y_pred_proba >= threshold).astype(int)
        f1 = f1_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)

        f1_scores.append(f1)
        precision_scores.append(precision)
        recall_scores.append(recall)

    # 找到最佳F1阈值
    best_f1_idx = np.argmax(f1_scores)
    best_threshold = thresholds[best_f1_idx]
    best_f1 = f1_scores[best_f1_idx]

    print(f"   当前阈值 0.1 的F1: {f1_scores[5]:.3f}")  # 0.1对应索引5
    print(f"   最佳阈值: {best_threshold:.3f}")
    print(f"   最佳F1: {best_f1:.3f}")
    print(f"   F1提升: {best_f1 - f1_scores[5]:.3f}")

    return {
        'thresholds': thresholds.tolist(),
        'f1_scores': f1_scores,
        'precision_scores': precision_scores,
        'recall_scores': recall_scores,
        'best_threshold': float(best_threshold),
        'best_f1': float(best_f1),
        'y_pred_proba': y_pred_proba.tolist()
    }

def evaluate_with_threshold(model, X_test, y_test, threshold):
    """使用指定阈值评估模型"""
    print(f"\n📊 使用阈值 {threshold:.3f} 评估模型...")

    # 获取模型期望的特征名称并重新排序
    expected_features = model.get_booster().feature_names
    missing_features = [f for f in expected_features if f not in X_test.columns]

    if missing_features:
        for feature in missing_features:
            X_test[feature] = 0

    X_test_ordered = X_test[expected_features]

    # 进行预测
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]
    y_pred = (y_pred_proba >= threshold).astype(int)

    # 计算性能指标
    f1 = f1_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, zero_division=0)
    recall = recall_score(y_test, y_pred, zero_division=0)
    auc = roc_auc_score(y_test, y_pred_proba)

    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")

    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")

    return {
        'f1_score': float(f1),
        'precision': float(precision),
        'recall': float(recall),
        'auc_roc': float(auc),
        'confusion_matrix': cm.tolist(),
        'threshold': float(threshold),
        'y_pred_proba': y_pred_proba.tolist(),
        'y_pred': y_pred.tolist()
    }

def try_ensemble_methods(model, X_test, y_test, test_windows):
    """尝试集成方法改进预测"""
    print("\n🔗 尝试集成方法...")

    # 获取模型期望的特征名称并重新排序
    expected_features = model.get_booster().feature_names
    missing_features = [f for f in expected_features if f not in X_test.columns]

    if missing_features:
        for feature in missing_features:
            X_test[feature] = 0

    X_test_ordered = X_test[expected_features]

    # 基础预测
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]

    # 方法1: 滑动平均平滑
    window_size = 5
    smoothed_proba = np.convolve(y_pred_proba, np.ones(window_size)/window_size, mode='same')

    # 方法2: 基于邻近窗口的投票
    voting_proba = y_pred_proba.copy()
    for i in range(len(y_pred_proba)):
        start_idx = max(0, i - 2)
        end_idx = min(len(y_pred_proba), i + 3)
        voting_proba[i] = np.mean(y_pred_proba[start_idx:end_idx])

    return {
        'original': y_pred_proba,
        'smoothed': smoothed_proba,
        'voting': voting_proba
    }

def aggregate_to_point_level(test_windows, window_predictions):
    """聚合窗口预测到时间点级别"""
    print(f"\n🔄 聚合窗口预测到时间点级别...")

    # 获取原始数据的长度
    original_length = test_windows['window_start_idx'].max() + 30

    # 为每个原始时间点收集所有覆盖它的窗口预测
    point_predictions = {}
    point_labels = {}

    # 遍历每个窗口
    unique_windows = test_windows[['window_id', 'window_start_idx', 'window_end_idx']].drop_duplicates()

    for _, window_info in unique_windows.iterrows():
        window_id = int(window_info['window_id'])
        start_idx = int(window_info['window_start_idx'])
        end_idx = int(window_info['window_end_idx'])

        window_pred = window_predictions[window_id - 1]  # window_id从1开始

        # 获取该窗口的真实标签
        window_data = test_windows[test_windows['window_id'] == window_id]
        window_label = window_data['anomaly'].max()

        # 将窗口预测分配给窗口内的每个时间点
        for point_idx in range(start_idx, end_idx + 1):
            if point_idx not in point_predictions:
                point_predictions[point_idx] = []
                point_labels[point_idx] = []

            point_predictions[point_idx].append(window_pred)
            point_labels[point_idx].append(window_label)

    # 聚合每个时间点的预测结果（使用最大值）
    aggregated_predictions = []
    aggregated_labels = []

    for point_idx in range(original_length):
        if point_idx in point_predictions:
            max_pred = max(point_predictions[point_idx])
            label = point_labels[point_idx][0]

            aggregated_predictions.append(max_pred)
            aggregated_labels.append(label)
        else:
            aggregated_predictions.append(0.0)
            aggregated_labels.append(0)

    return np.array(aggregated_predictions), np.array(aggregated_labels)

def main():
    print("🚀 改进F1-Score测试...")
    print("=" * 60)

    # 1. 加载模型和配置
    model, threshold_config = load_model_and_config()

    # 2. 创建测试窗口
    test_windows = create_test_windows()
    if test_windows.empty:
        print("❌ 无法创建测试窗口，退出测试")
        return

    # 3. 提取特征
    test_agg_features, available_features = extract_features_like_training(test_windows)
    test_tsfresh_features = create_mock_tsfresh_features(test_agg_features, available_features)
    X_test = pd.concat([test_agg_features, test_tsfresh_features], axis=1)
    y_test = create_test_labels(test_windows)

    print(f"\n🔗 合并后特征形状: {X_test.shape}")

    # 4. 当前阈值性能
    print(f"\n{'='*20} 当前阈值 (0.1) 性能 {'='*20}")
    current_results = evaluate_with_threshold(model, X_test.copy(), y_test, 0.1)

    # 5. 优化阈值
    print(f"\n{'='*20} 阈值优化 {'='*20}")
    threshold_optimization = optimize_threshold_for_f1(model, X_test.copy(), y_test)

    # 6. 最佳阈值性能
    print(f"\n{'='*20} 最佳阈值性能 {'='*20}")
    best_results = evaluate_with_threshold(model, X_test.copy(), y_test, threshold_optimization['best_threshold'])

    # 7. 尝试集成方法
    print(f"\n{'='*20} 集成方法测试 {'='*20}")
    ensemble_predictions = try_ensemble_methods(model, X_test.copy(), y_test, test_windows)

    # 测试不同集成方法的效果
    ensemble_results = {}
    for method_name, predictions in ensemble_predictions.items():
        print(f"\n--- {method_name.upper()} 方法 ---")

        # 使用最佳阈值测试
        y_pred = (np.array(predictions) >= threshold_optimization['best_threshold']).astype(int)
        f1 = f1_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)

        print(f"   F1-Score: {f1:.3f}")
        print(f"   Precision: {precision:.3f}")
        print(f"   Recall: {recall:.3f}")

        ensemble_results[method_name] = {
            'f1_score': float(f1),
            'precision': float(precision),
            'recall': float(recall)
        }

    # 8. 时间点级别评估
    print(f"\n{'='*20} 时间点级别评估 {'='*20}")

    # 使用最佳方法进行时间点级别聚合
    best_method = max(ensemble_results.keys(), key=lambda k: ensemble_results[k]['f1_score'])
    print(f"   最佳集成方法: {best_method}")

    point_predictions, point_labels = aggregate_to_point_level(
        test_windows,
        ensemble_predictions[best_method]
    )

    # 时间点级别性能
    point_pred_binary = (point_predictions >= threshold_optimization['best_threshold']).astype(int)
    point_f1 = f1_score(point_labels, point_pred_binary)
    point_precision = precision_score(point_labels, point_pred_binary, zero_division=0)
    point_recall = recall_score(point_labels, point_pred_binary, zero_division=0)

    print(f"   时间点级别 F1-Score: {point_f1:.3f}")
    print(f"   时间点级别 Precision: {point_precision:.3f}")
    print(f"   时间点级别 Recall: {point_recall:.3f}")

    # 9. 保存结果
    final_results = {
        'current_threshold_results': current_results,
        'threshold_optimization': threshold_optimization,
        'best_threshold_results': best_results,
        'ensemble_results': ensemble_results,
        'point_level_results': {
            'f1_score': float(point_f1),
            'precision': float(point_precision),
            'recall': float(point_recall),
            'best_method': best_method
        },
        'improvements': {
            'f1_improvement': float(best_results['f1_score'] - current_results['f1_score']),
            'point_f1_improvement': float(point_f1 - current_results['f1_score'])
        }
    }

    with open('results/f1_improvement_results.json', 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\n💾 结果已保存到: results/f1_improvement_results.json")
    print("\n📊 改进总结:")
    print(f"   原始F1-Score: {current_results['f1_score']:.3f}")
    print(f"   优化后F1-Score: {best_results['f1_score']:.3f}")
    print(f"   时间点F1-Score: {point_f1:.3f}")
    print(f"   F1提升: {final_results['improvements']['f1_improvement']:.3f}")
    print("✅ F1优化测试完成！")

if __name__ == "__main__":
    main()
