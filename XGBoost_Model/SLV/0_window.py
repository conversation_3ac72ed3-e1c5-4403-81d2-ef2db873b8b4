"""
更新版电芯不平衡异常窗口检测器
- 固定30行数据窗口
- 滑动步长5行
- 分层时序分割训练/验证集
- 输出测试集原始数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import json
from tqdm import tqdm

class StringImbalanceWindowDetector:
    def __init__(self, csv_path, window_length=30, stride=5, pos_target=50, neg_target=150):
        """
        初始化电芯不平衡窗口检测器
        
        Args:
            csv_path: CSV文件路径
            window_length: 窗口长度（行数）
            stride: 滑动步长（行数）
            pos_target: 异常窗口目标数量
            neg_target: 正常窗口目标数量
        """
        self.csv_path = csv_path
        self.L = window_length
        self.S = stride
        self.pos_target = pos_target
        self.neg_target = neg_target
        
    def load_and_preprocess_data(self):
        """加载并预处理数据"""
        print("📁 加载数据...")
        df = pd.read_csv(self.csv_path)
        
        # 转换时间戳
        df['ts'] = pd.to_datetime(df['ts'])
        df = df.sort_values('ts').reset_index(drop=True)
        
        print(f"原始数据: {len(df)} 行")
        print(f"时间范围: {df['ts'].min()} 至 {df['ts'].max()}")
        
        return df
    
    def detect_string_imbalance(self, df):
        """检测电芯不平衡异常"""
        print("🔍 检测电芯不平衡异常...")
        
        # 电芯不平衡异常定义
        df['is_string_imbalance'] = (
            (df['cellvdelta'] >= 0.3) & 
            (df['averagecellv'] >= 3.1)
        )
        
        imbalance_count = df['is_string_imbalance'].sum()
        imbalance_rate = (imbalance_count / len(df)) * 100
        
        print(f"检测到电芯不平衡: {imbalance_count} 条 ({imbalance_rate:.2f}%)")
        
        return df
    
    def generate_fixed_row_windows(self, df):
        """生成固定行数的滑动窗口"""
        print(f"🪟 生成固定行数窗口 (长度={self.L}行, 步长={self.S}行)...")
        
        anomaly_windows = []  # 异常窗口
        normal_windows = []   # 正常窗口
        
        pos_count = 0
        neg_count = 0
        
        start_idx = 0
        total_possible = (len(df) - self.L) // self.S + 1
        
        pbar = tqdm(total=min(total_possible, self.pos_target + self.neg_target), desc="生成窗口")
        
        while start_idx + self.L <= len(df):
            # 固定30行窗口
            window = df.iloc[start_idx:start_idx + self.L].copy()
            
            # 判断是否为异常窗口（窗口内有任何电芯不平衡）
            has_imbalance = window['is_string_imbalance'].any()
            
            # 添加窗口信息
            window_info = {
                'window': window,
                'start_idx': start_idx,
                'end_idx': start_idx + self.L - 1,
                'ts_start': window['ts'].iloc[0],
                'ts_end': window['ts'].iloc[-1],
                'imbalance_count': window['is_string_imbalance'].sum(),
                'has_imbalance': has_imbalance
            }
            
            # 根据需要收集窗口
            if has_imbalance and pos_count < self.pos_target:
                anomaly_windows.append(window_info)
                pos_count += 1
                pbar.update(1)
            elif not has_imbalance and neg_count < self.neg_target:
                normal_windows.append(window_info)
                neg_count += 1
                pbar.update(1)
            
            # 收集够了就停止
            if pos_count >= self.pos_target and neg_count >= self.neg_target:
                break
            
            start_idx += self.S
        
        pbar.close()
        
        # 记录停止位置
        stop_idx = start_idx + self.L
        stop_timestamp = df.iloc[min(stop_idx, len(df)-1)]['ts']
        
        print(f"✅ 窗口生成完成:")
        print(f"  异常窗口: {len(anomaly_windows)} 个")
        print(f"  正常窗口: {len(normal_windows)} 个")
        print(f"  停止位置: 第{stop_idx}行 ({stop_timestamp})")
        
        return anomaly_windows, normal_windows, stop_idx, stop_timestamp
    
    def stratified_temporal_split(self, anomaly_windows, normal_windows):
        """分层时序分割"""
        print("🔄 进行分层时序分割...")
        
        # 确保窗口按时间排序
        anomaly_windows.sort(key=lambda x: x['ts_start'])
        normal_windows.sort(key=lambda x: x['ts_start'])
        
        # 异常窗口分割 (80/20)
        anomaly_split = int(len(anomaly_windows) * 0.8)
        train_anomaly = anomaly_windows[:anomaly_split]
        val_anomaly = anomaly_windows[anomaly_split:]
        
        # 正常窗口分割 (80/20)
        normal_split = int(len(normal_windows) * 0.8)
        train_normal = normal_windows[:normal_split]
        val_normal = normal_windows[normal_split:]
        
        print(f"📊 分割结果:")
        print(f"  训练集: {len(train_anomaly)}异常 + {len(train_normal)}正常 = {len(train_anomaly) + len(train_normal)}个")
        print(f"  验证集: {len(val_anomaly)}异常 + {len(val_normal)}正常 = {len(val_anomaly) + len(val_normal)}个")
        
        # 计算比例验证
        train_anomaly_rate = len(train_anomaly) / (len(train_anomaly) + len(train_normal))
        val_anomaly_rate = len(val_anomaly) / (len(val_anomaly) + len(val_normal))
        print(f"  训练集异常率: {train_anomaly_rate:.1%}")
        print(f"  验证集异常率: {val_anomaly_rate:.1%}")
        
        return train_anomaly + train_normal, val_anomaly + val_normal
    
    def prepare_window_data(self, windows, dataset_type, start_window_id=1):
        """准备窗口数据集"""
        all_data = []
        
        for i, window_info in enumerate(windows):
            window_data = window_info['window'].copy()
            
            # 添加窗口标识 - 确保window_id不重叠
            window_data['window_id'] = start_window_id + i
            window_data['label'] = int(window_info['has_imbalance'])
            window_data['dataset_type'] = dataset_type
            
            all_data.append(window_data)
        
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def export_datasets(self, train_windows, val_windows, df, stop_idx, stop_timestamp):
        """导出训练/验证/测试数据集"""
        print("💾 导出数据集...")
        
        # 创建输出目录
        output_dir = "data/processed_data"
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 训练集 - window_id从1开始
        train_df = self.prepare_window_data(train_windows, 'train', start_window_id=1)
        train_path = os.path.join(output_dir, 'train_windows.csv')
        train_df.to_csv(train_path, index=False)
        print(f"  训练集: {train_path} ({len(train_df)} 行)")
        print(f"    训练集window_id范围: {train_df['window_id'].min()} - {train_df['window_id'].max()}")
        
        # 2. 验证集 - window_id从训练集结束后开始
        val_start_id = len(train_windows) + 1
        val_df = self.prepare_window_data(val_windows, 'val', start_window_id=val_start_id)
        val_path = os.path.join(output_dir, 'val_windows.csv')
        val_df.to_csv(val_path, index=False)
        print(f"  验证集: {val_path} ({len(val_df)} 行)")
        print(f"    验证集window_id范围: {val_df['window_id'].min()} - {val_df['window_id'].max()}")
        
        # 3. 测试集原始数据
        test_df = df.iloc[stop_idx:].copy().reset_index(drop=True)
        test_path = os.path.join(output_dir, 'test_raw_data.csv')
        test_df.to_csv(test_path, index=False)
        print(f"  测试集: {test_path} ({len(test_df)} 行)")
        
        # 验证window_id不重叠
        train_ids = set(train_df['window_id'].unique())
        val_ids = set(val_df['window_id'].unique())
        overlap = train_ids.intersection(val_ids)
        
        if overlap:
            print(f"  ⚠️  警告: 发现重叠的window_id: {overlap}")
        else:
            print(f"  ✅ window_id无重叠，分割正确")
        
        # 4. 分割信息
        split_info = {
            'split_timestamp': datetime.now().isoformat(),
            'window_config': {
                'window_length': self.L,
                'stride': self.S,
                'pos_target': self.pos_target,
                'neg_target': self.neg_target
            },
            'split_results': {
                'train_windows': len(train_windows),
                'val_windows': len(val_windows),
                'train_anomaly_count': sum(1 for w in train_windows if w['has_imbalance']),
                'val_anomaly_count': sum(1 for w in val_windows if w['has_imbalance']),
                'stop_index': stop_idx,
                'stop_timestamp': stop_timestamp.isoformat(),
                'test_data_rows': len(test_df)
            },
            'data_quality': {
                'train_anomaly_rate': sum(1 for w in train_windows if w['has_imbalance']) / len(train_windows),
                'val_anomaly_rate': sum(1 for w in val_windows if w['has_imbalance']) / len(val_windows),
                'test_anomaly_rate': test_df['is_string_imbalance'].sum() / len(test_df) if len(test_df) > 0 else 0
            },
            'window_id_ranges': {
                'train_range': [int(train_df['window_id'].min()), int(train_df['window_id'].max())],
                'val_range': [int(val_df['window_id'].min()), int(val_df['window_id'].max())]
            }
        }
        
        split_info_path = os.path.join(output_dir, 'split_info.json')
        with open(split_info_path, 'w', encoding='utf-8') as f:
            json.dump(split_info, f, indent=2, ensure_ascii=False, default=str)
        print(f"  分割信息: {split_info_path}")
        
        return train_path, val_path, test_path, split_info_path
    
    def generate_summary_report(self, train_windows, val_windows, test_data_len, split_info):
        """生成汇总报告"""
        print("\n" + "="*60)
        print("📊 电芯不平衡异常窗口分割报告")
        print("="*60)
        
        print(f"\n🪟 窗口配置:")
        print(f"  窗口长度: {self.L} 行")
        print(f"  滑动步长: {self.S} 行")
        print(f"  重叠比例: {((self.L - self.S) / self.L) * 100:.1f}%")
        
        print(f"\n📈 数据分割结果:")
        print(f"  训练窗口: {len(train_windows)} 个")
        print(f"  验证窗口: {len(val_windows)} 个")
        print(f"  测试原始数据: {test_data_len} 行")
        
        train_anomaly = sum(1 for w in train_windows if w['has_imbalance'])
        val_anomaly = sum(1 for w in val_windows if w['has_imbalance'])
        
        print(f"\n🎯 标签分布:")
        print(f"  训练集: {train_anomaly}异常 + {len(train_windows)-train_anomaly}正常")
        print(f"  验证集: {val_anomaly}异常 + {len(val_windows)-val_anomaly}正常")
        print(f"  训练集异常率: {train_anomaly/len(train_windows):.1%}")
        print(f"  验证集异常率: {val_anomaly/len(val_windows):.1%}")
        
        if 'test_anomaly_rate' in split_info['data_quality']:
            test_rate = split_info['data_quality']['test_anomaly_rate']
            print(f"  测试集异常率: {test_rate:.1%} (真实分布)")
        
        # 显示window_id分布
        if 'window_id_ranges' in split_info:
            train_range = split_info['window_id_ranges']['train_range']
            val_range = split_info['window_id_ranges']['val_range']
            print(f"\n🆔 窗口ID分布:")
            print(f"  训练集window_id: {train_range[0]} - {train_range[1]}")
            print(f"  验证集window_id: {val_range[0]} - {val_range[1]}")
            print(f"  ✅ 无重叠，分割正确")
    
    def run(self):
        """运行完整的检测和分割流程"""
        print("🚀 启动电芯不平衡异常窗口检测与数据分割...")
        
        try:
            # 1. 加载数据
            df = self.load_and_preprocess_data()
            
            # 2. 检测异常
            df = self.detect_string_imbalance(df)
            
            # 3. 生成窗口
            anomaly_windows, normal_windows, stop_idx, stop_timestamp = self.generate_fixed_row_windows(df)
            
            # 4. 分层时序分割
            train_windows, val_windows = self.stratified_temporal_split(anomaly_windows, normal_windows)
            
            # 5. 导出数据集
            train_path, val_path, test_path, split_info_path = self.export_datasets(
                train_windows, val_windows, df, stop_idx, stop_timestamp
            )
            
            # 6. 生成报告
            with open(split_info_path, 'r') as f:
                split_info = json.load(f)
            
            self.generate_summary_report(train_windows, val_windows, len(df) - stop_idx, split_info)
            
            print(f"\n✅ 数据分割完成！")
            print(f"📁 训练集: {train_path}")
            print(f"📁 验证集: {val_path}")
            print(f"📁 测试集: {test_path}")
            print(f"📋 分割信息: {split_info_path}")
            print(f"\n🎯 接下来可以运行: python 1_extract_tsfresh_features.py")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 流程执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

# 主程序
if __name__ == "__main__":
    # 配置参数
    CSV_PATH = "data/processed_data/continuous_2024H1_MAC_8C_1F_64_C5_12_52.csv"
    
    # 创建检测器
    detector = StringImbalanceWindowDetector(
        csv_path=CSV_PATH,
        window_length=30,    # 30行窗口
        stride=5,           # 5行步长
        pos_target=50,      # 50个异常窗口
        neg_target=150      # 150个正常窗口
    )
    
    # 运行检测和分割
    success = detector.run()
    if not success:
        exit(1)
